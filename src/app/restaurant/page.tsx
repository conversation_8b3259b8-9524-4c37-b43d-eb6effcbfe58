import type { Metadata } from 'next/types';
import { Container, Stack, Text, Title } from '@mantine/core';

export const metadata: Metadata = {
  title: 'Restaurant',
  description: 'Restaurant information and listings',
};

const Restaurant = () => {
  return (
    <Container size="lg" py={40}>
      <Stack gap={24}>
        <Title order={1} size="h1" fw={700}>
          Restaurant
        </Title>
        <Text size="lg" c="gray.7">
          Discover amazing restaurants and culinary experiences.
        </Text>
        <Text c="gray.6">
          This page will feature restaurant listings, reviews, and booking functionality.
        </Text>
      </Stack>
    </Container>
  );
};

export default Restaurant;
